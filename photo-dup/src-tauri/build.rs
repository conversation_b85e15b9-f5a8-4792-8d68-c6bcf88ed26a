use std::env;
use std::path::Path;
use std::process::Command;

fn main() {
    // 编译 Swift 代码
    compile_swift_code();
    
    // 运行 Tauri 构建
    tauri_build::build();
}

fn compile_swift_code() {
    let out_dir = env::var("OUT_DIR").unwrap();
    let swift_dir = Path::new("swift");
    
    // 创建输出目录
    std::fs::create_dir_all(&out_dir).unwrap();
    
    // 编译 Swift 库
    let status = Command::new("swift")
        .args(&[
            "build",
            "--package-path",
            swift_dir.to_str().unwrap(),
            "--configuration",
            "release"
        ])
        .status()
        .expect("Failed to execute swift build");
    
    if !status.success() {
        panic!("Swift build failed");
    }
    
    // 链接编译后的动态库
    println!("cargo:rustc-link-search=native={}/.build/release", swift_dir.display());
    println!("cargo:rustc-link-lib=dylib=PhotoGalleryBridge");
    
    // 链接 macOS 系统框架
    println!("cargo:rustc-link-lib=framework=Foundation");
    println!("cargo:rustc-link-lib=framework=Photos");
}
