---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/libPhotoGalleryBridge.dylib'
relocations:
  - { offset: 0x10E202, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge13lastErrorInfo33_3AE31ED88418F257D5A36F2DDFC489ADLLAA0eF0VSgvp', symObjAddr: 0x18A20, symBinAddr: 0xC8F0, symSize: 0x0 }
  - { offset: 0x10E217, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructV10identifierSPys4Int8VGSgvpfi', symObjAddr: 0x0, symBinAddr: 0x20E4, symSize: 0x8 }
  - { offset: 0x10E22F, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructV9file_nameSPys4Int8VGSgvpfi', symObjAddr: 0x8, symBinAddr: 0x20EC, symSize: 0x8 }
  - { offset: 0x10E247, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructV4dataSPys5UInt8VGSgvpfi', symObjAddr: 0x10, symBinAddr: 0x20F4, symSize: 0x8 }
  - { offset: 0x10E25F, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayV6assetsSpyAA0aD6StructVGSgvpfi', symObjAddr: 0x18, symBinAddr: 0x20FC, symSize: 0x8 }
  - { offset: 0x10E277, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x20, symBinAddr: 0x2104, symSize: 0x3C }
  - { offset: 0x10E28F, size: 0x8, addend: 0x0, symName: '_$sSo7PHAssetCSiSpy10ObjectiveC8ObjCBoolVGIeggyy_ABSiAFIeyByyy_TR', symObjAddr: 0x5C, symBinAddr: 0x2140, symSize: 0x68 }
  - { offset: 0x10E2A7, size: 0x8, addend: 0x0, symName: '_$sSo7NSImageCSgSDys11AnyHashableVypGSgIeggg_ACSo12NSDictionaryCSgIeyByy_TR', symObjAddr: 0xC4, symBinAddr: 0x21A8, symSize: 0x9C }
  - { offset: 0x10E43D, size: 0x8, addend: 0x0, symName: _request_photo_gallery_permission, symObjAddr: 0x170, symBinAddr: 0x2254, symSize: 0x1D0 }
  - { offset: 0x10E4AD, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC29requestPermissionForFirstTime33_3AE31ED88418F257D5A36F2DDFC489ADLLSbyFySo21PHAuthorizationStatusVcfU_TA', symObjAddr: 0x354, symBinAddr: 0x2438, symSize: 0x54 }
  - { offset: 0x10E4F3, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3A8, symBinAddr: 0x248C, symSize: 0x10 }
  - { offset: 0x10E507, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3B8, symBinAddr: 0x249C, symSize: 0x8 }
  - { offset: 0x10E5B5, size: 0x8, addend: 0x0, symName: _fetch_photo_assets, symObjAddr: 0x3C0, symBinAddr: 0x24A4, symSize: 0x61C }
  - { offset: 0x10EC74, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x9DC, symBinAddr: 0x2AC0, symSize: 0x44 }
  - { offset: 0x10ECA9, size: 0x8, addend: 0x0, symName: _generate_thumbnail, symObjAddr: 0xA20, symBinAddr: 0x2B04, symSize: 0x2DC }
  - { offset: 0x10EFB2, size: 0x8, addend: 0x0, symName: _get_photo_info, symObjAddr: 0xCFC, symBinAddr: 0x2DE0, symSize: 0x30C }
  - { offset: 0x10F2A2, size: 0x8, addend: 0x0, symName: _free_photo_asset_array, symObjAddr: 0x1008, symBinAddr: 0x30EC, symSize: 0x8C }
  - { offset: 0x10F401, size: 0x8, addend: 0x0, symName: _get_last_error_message, symObjAddr: 0x10E0, symBinAddr: 0x31C4, symSize: 0x15C }
  - { offset: 0x10F6AA, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19getLastErrorMessageSPys4Int8VGSgyFAfEXEfU_', symObjAddr: 0x123C, symBinAddr: 0x3320, symSize: 0x6C }
  - { offset: 0x10F791, size: 0x8, addend: 0x0, symName: _get_last_error_code, symObjAddr: 0x12A8, symBinAddr: 0x338C, symSize: 0x1C }
  - { offset: 0x10F862, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo7PHAssetC_Ttg5', symObjAddr: 0x167C, symBinAddr: 0x3760, symSize: 0x6C }
  - { offset: 0x10F87A, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo16NSSortDescriptorC_Ttg5', symObjAddr: 0x16E8, symBinAddr: 0x37CC, symSize: 0x6C }
  - { offset: 0x10F8A8, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyFSo7PHAssetC_Tg5', symObjAddr: 0x1754, symBinAddr: 0x3838, symSize: 0x70 }
  - { offset: 0x10F985, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo7PHAssetC_Tg5', symObjAddr: 0x17C4, symBinAddr: 0x38A8, symSize: 0x128 }
  - { offset: 0x10FAB3, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo7PHAssetC_Tt1g5', symObjAddr: 0x18EC, symBinAddr: 0x39D0, symSize: 0x80 }
  - { offset: 0x10FB0C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo7PHAssetC_Tg5', symObjAddr: 0x196C, symBinAddr: 0x3A50, symSize: 0x118 }
  - { offset: 0x10FBEC, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo27NSBitmapImageRepPropertyKeya_Tg5', symObjAddr: 0x1A84, symBinAddr: 0x3B68, symSize: 0x80 }
  - { offset: 0x10FC75, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo27NSBitmapImageRepPropertyKeya_Tg5', symObjAddr: 0x1B04, symBinAddr: 0x3BE8, symSize: 0x178 }
  - { offset: 0x10FD65, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo15PHAssetResourceC_Tg5', symObjAddr: 0x1C7C, symBinAddr: 0x3D60, symSize: 0x1D4 }
  - { offset: 0x10FDE6, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo7PHAssetC_Tg5', symObjAddr: 0x1E50, symBinAddr: 0x3F34, symSize: 0x1D4 }
  - { offset: 0x10FE88, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x22E4, symBinAddr: 0x43C8, symSize: 0x48 }
  - { offset: 0x10FE9C, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x232C, symBinAddr: 0x4410, symSize: 0x40 }
  - { offset: 0x10FEB0, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge9ErrorInfoVSgWOe', symObjAddr: 0x236C, symBinAddr: 0x4450, symSize: 0x30 }
  - { offset: 0x10FF53, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x2CE4, symBinAddr: 0x4DC8, symSize: 0x14 }
  - { offset: 0x10FF67, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x2CF8, symBinAddr: 0x4DDC, symSize: 0x40 }
  - { offset: 0x10FF7B, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A8InfoDataVSgWOs', symObjAddr: 0x2D38, symBinAddr: 0x4E1C, symSize: 0x28 }
  - { offset: 0x10FF8F, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x2D60, symBinAddr: 0x4E44, symSize: 0x4 }
  - { offset: 0x10FFA3, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0x2D64, symBinAddr: 0x4E48, symSize: 0x14 }
  - { offset: 0x10FFB7, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructVwet', symObjAddr: 0x2D78, symBinAddr: 0x4E5C, symSize: 0x54 }
  - { offset: 0x10FFCB, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructVwst', symObjAddr: 0x2DCC, symBinAddr: 0x4EB0, symSize: 0x50 }
  - { offset: 0x10FFDF, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructVMa', symObjAddr: 0x2E1C, symBinAddr: 0x4F00, symSize: 0x10 }
  - { offset: 0x10FFF3, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVwCP', symObjAddr: 0x2E2C, symBinAddr: 0x4F10, symSize: 0x2C }
  - { offset: 0x110007, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x2E58, symBinAddr: 0x4F3C, symSize: 0x14 }
  - { offset: 0x11001B, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVwet', symObjAddr: 0x2E6C, symBinAddr: 0x4F50, symSize: 0x20 }
  - { offset: 0x11002F, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVwst', symObjAddr: 0x2E8C, symBinAddr: 0x4F70, symSize: 0x34 }
  - { offset: 0x110043, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVMa', symObjAddr: 0x2EC0, symBinAddr: 0x4FA4, symSize: 0x10 }
  - { offset: 0x110057, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x2ED0, symBinAddr: 0x4FB4, symSize: 0x14 }
  - { offset: 0x11006B, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructVwet', symObjAddr: 0x2EE4, symBinAddr: 0x4FC8, symSize: 0x20 }
  - { offset: 0x11007F, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructVwst', symObjAddr: 0x2F04, symBinAddr: 0x4FE8, symSize: 0x2C }
  - { offset: 0x110093, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructVMa', symObjAddr: 0x2F30, symBinAddr: 0x5014, symSize: 0x10 }
  - { offset: 0x1100A7, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x2F40, symBinAddr: 0x5024, symSize: 0xC }
  - { offset: 0x1100BB, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayVwet', symObjAddr: 0x2F4C, symBinAddr: 0x5030, symSize: 0x20 }
  - { offset: 0x1100CF, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayVwst', symObjAddr: 0x2F6C, symBinAddr: 0x5050, symSize: 0x28 }
  - { offset: 0x1100E3, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayVMa', symObjAddr: 0x2F94, symBinAddr: 0x5078, symSize: 0x10 }
  - { offset: 0x1100F7, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerCMa', symObjAddr: 0x2FA4, symBinAddr: 0x5088, symSize: 0x20 }
  - { offset: 0x11014D, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC17generateThumbnail3for10targetSize10Foundation4DataVSgSo7PHAssetC_So6CGSizeVtFySo7NSImageCSg_SDys11AnyHashableVypGSgtcfU_TA', symObjAddr: 0x3024, symBinAddr: 0x5108, symSize: 0x26C }
  - { offset: 0x11025F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x3290, symBinAddr: 0x5374, symSize: 0x14 }
  - { offset: 0x110273, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x32A4, symBinAddr: 0x5388, symSize: 0x40 }
  - { offset: 0x1102F5, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaMa', symObjAddr: 0x3410, symBinAddr: 0x54F4, symSize: 0x50 }
  - { offset: 0x110309, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeya_yptWOc', symObjAddr: 0x3460, symBinAddr: 0x5544, symSize: 0x48 }
  - { offset: 0x11031D, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x34A8, symBinAddr: 0x558C, symSize: 0x10 }
  - { offset: 0x11035D, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC05fetchA6AssetsSaySo7PHAssetCGyFyAF_SiSpy10ObjectiveC8ObjCBoolVGtcfU_TA', symObjAddr: 0x351C, symBinAddr: 0x5600, symSize: 0xA8 }
  - { offset: 0x11043E, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x35C8, symBinAddr: 0x56AC, symSize: 0x24 }
  - { offset: 0x110452, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x35EC, symBinAddr: 0x56D0, symSize: 0x24 }
  - { offset: 0x110466, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSHSCSQWb', symObjAddr: 0x3610, symBinAddr: 0x56F4, symSize: 0x24 }
  - { offset: 0x11072A, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x12D0, symBinAddr: 0x33B4, symSize: 0x4 }
  - { offset: 0x11074D, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo27NSBitmapImageRepPropertyKeya_Tt1gq5', symObjAddr: 0x12D4, symBinAddr: 0x33B8, symSize: 0x84 }
  - { offset: 0x1107DF, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x1358, symBinAddr: 0x343C, symSize: 0x4 }
  - { offset: 0x1107FB, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo27NSBitmapImageRepPropertyKeya_Tt1gq5', symObjAddr: 0x135C, symBinAddr: 0x3440, symSize: 0x8C }
  - { offset: 0x11089C, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromF1Cyx01_F5CTypeQzSgFZTW', symObjAddr: 0x13E8, symBinAddr: 0x34CC, symSize: 0x40 }
  - { offset: 0x11091B, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1470, symBinAddr: 0x3554, symSize: 0x40 }
  - { offset: 0x110999, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x14B0, symBinAddr: 0x3594, symSize: 0x70 }
  - { offset: 0x110A0F, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x1520, symBinAddr: 0x3604, symSize: 0x84 }
  - { offset: 0x110AB1, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0x1610, symBinAddr: 0x36F4, symSize: 0x6C }
  - { offset: 0x110C2E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo27NSBitmapImageRepPropertyKeya_ypTt0g5Tf4g_n', symObjAddr: 0x32E4, symBinAddr: 0x53C8, symSize: 0xF0 }
  - { offset: 0x110EFB, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerCfD', symObjAddr: 0x160, symBinAddr: 0x2244, symSize: 0x10 }
  - { offset: 0x1112BE, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSYSCSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x15A4, symBinAddr: 0x3688, symSize: 0x44 }
  - { offset: 0x1112E8, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSYSCSY8rawValue03RawG0QzvgTW', symObjAddr: 0x15E8, symBinAddr: 0x36CC, symSize: 0x28 }
  - { offset: 0x1113B9, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC05fetchA6AssetsSaySo7PHAssetCGyFTf4d_n', symObjAddr: 0x2024, symBinAddr: 0x4108, symSize: 0x2C0 }
  - { offset: 0x111541, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC03getA4Info3forAA0aF4DataVSgSo7PHAssetC_tFTf4nd_n', symObjAddr: 0x239C, symBinAddr: 0x4480, symSize: 0x6C8 }
  - { offset: 0x11189A, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC17generateThumbnail3for10targetSize10Foundation4DataVSgSo7PHAssetC_So6CGSizeVtFTf4nnd_n', symObjAddr: 0x2A64, symBinAddr: 0x4B48, symSize: 0x280 }
  - { offset: 0x111BAD, size: 0x8, addend: 0x0, symName: __swift_stdlib_malloc_size, symObjAddr: 0x35C4, symBinAddr: 0x56A8, symSize: 0x4 }
  - { offset: 0x111BBB, size: 0x8, addend: 0x0, symName: __swift_stdlib_malloc_size, symObjAddr: 0x35C4, symBinAddr: 0x56A8, symSize: 0x4 }
...
