// Tauri 命令实现模块
use tauri::State;
use std::sync::Mutex;
use crate::models::*;
use crate::cache;

// 应用状态管理
pub struct AppState {
    gallery_manager: crate::swift_bridge::SwiftPhotoGalleryBridge,
    cache_manager: cache::CacheManager,
    has_permission: Mutex<bool>,
}

impl AppState {
    pub fn new() -> Self {
        let cache_config = cache::CacheConfig::default();
        let cache_manager = cache::CacheManager::new(cache_config)
            .expect("Failed to initialize cache manager");
        
        AppState {
            gallery_manager: crate::swift_bridge::SwiftPhotoGalleryBridge::new(),
            cache_manager,
            has_permission: Mutex::new(false),
        }
    }
}

// 请求相册访问权限
#[tauri::command]
pub async fn request_permission(state: State<'_, AppState>) -> Result<bool, String> {
    match state.gallery_manager.request_permission() {
        Ok(granted) => {
            let mut permission = state.has_permission.lock().unwrap();
            *permission = granted;
            Ok(granted)
        }
        Err(e) => Err(format!("请求权限失败: {:?}", e)),
    }
}

// 获取相片资源列表
#[tauri::command]
pub async fn get_photo_assets(state: State<'_, AppState>) -> Result<Vec<PhotoAssetJson>, String> {
    let has_permission = *state.has_permission.lock().unwrap();
    
    if !has_permission {
        return Err("需要相册访问权限".to_string());
    }

    match state.gallery_manager.fetch_photo_assets() {
        Ok(assets) => {
            let json_assets: Vec<PhotoAssetJson> = assets
                .into_iter()
                .map(PhotoAssetJson::from)
                .collect();
            Ok(json_assets)
        }
        Err(e) => Err(format!("获取相片失败: {:?}", e)),
    }
}

// 获取缩略图
#[tauri::command]
pub async fn get_thumbnail(
    asset_id: String,
    width: u32,
    height: u32,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let has_permission = *state.has_permission.lock().unwrap();
    
    if !has_permission {
        return Err("需要相册访问权限".to_string());
    }

    // 首先尝试从缓存获取
    match state.cache_manager.get_thumbnail(&asset_id, (width, height)).await {
        Ok(cached_thumbnail) => {
            let json_thumbnail = ThumbnailDataJson::from(cached_thumbnail);
            Ok(json_thumbnail.data)
        }
        Err(cache_error) => {
            log::debug!("缓存未命中，生成新缩略图: {:?}", cache_error);
            
            // 缓存未命中，生成新的缩略图
            match state.gallery_manager.generate_thumbnail(&asset_id, width, height) {
                Ok(thumbnail_data) => {
                    // 存储到缓存
                    if let Err(cache_err) = state.cache_manager.put_thumbnail(&asset_id, (width, height), thumbnail_data.clone()).await {
                        log::warn!("缓存缩略图失败: {:?}", cache_err);
                    }
                    
                    let json_thumbnail = ThumbnailDataJson::from(thumbnail_data);
                    Ok(json_thumbnail.data)
                }
                Err(e) => Err(format!("生成缩略图失败: {:?}", e)),
            }
        }
    }
}

// 获取相片详细信息
#[tauri::command]
pub async fn get_photo_info(
    asset_id: String,
    state: State<'_, AppState>,
) -> Result<PhotoInfoJson, String> {
    let has_permission = *state.has_permission.lock().unwrap();
    
    if !has_permission {
        return Err("需要相册访问权限".to_string());
    }

    match state.gallery_manager.get_photo_info(&asset_id) {
        Ok(photo_info) => {
            let json_info = PhotoInfoJson::from(photo_info);
            Ok(json_info)
        }
        Err(e) => Err(format!("获取相片信息失败: {:?}", e)),
    }
}

// 搜索相片
#[tauri::command]
pub async fn search_photos(
    query: String,
    state: State<'_, AppState>,
) -> Result<Vec<PhotoAssetJson>, String> {
    let has_permission = *state.has_permission.lock().unwrap();
    
    if !has_permission {
        return Err("需要相册访问权限".to_string());
    }

    // 获取所有相片并过滤
    match state.gallery_manager.fetch_photo_assets() {
        Ok(assets) => {
            let filtered_assets: Vec<PhotoAssetJson> = assets
                .into_iter()
                .filter(|asset| {
                    let query_lower = query.to_lowercase();
                    asset.id.to_lowercase().contains(&query_lower) ||
                    asset.file_name
                        .as_ref()
                        .map_or(false, |name| name.to_lowercase().contains(&query_lower))
                })
                .map(PhotoAssetJson::from)
                .collect();
            Ok(filtered_assets)
        }
        Err(e) => Err(format!("搜索相片失败: {:?}", e)),
    }
}

// 获取缓存统计信息
#[tauri::command]
pub async fn get_cache_stats(state: State<'_, AppState>) -> Result<String, String> {
    let stats = state.cache_manager.get_stats().await;
    let stats_json = serde_json::json!({
        "memory_cache_size": stats.memory_cache_size,
        "disk_cache_size": stats.disk_cache_size,
        "memory_usage_mb": format!("{:.2}", stats.memory_usage_mb()),
        "disk_usage_mb": format!("{:.2}", stats.disk_usage_mb())
    });
    Ok(serde_json::to_string(&stats_json).unwrap())
}

// 清除所有缓存
#[tauri::command]
pub async fn clear_cache(state: State<'_, AppState>) -> Result<(), String> {
    match state.cache_manager.clear_all().await {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("清除缓存失败: {:?}", e)),
    }
}

// 清理过期缓存
#[tauri::command]
pub async fn cleanup_expired_cache(state: State<'_, AppState>) -> Result<String, String> {
    match state.cache_manager.cleanup_expired_cache().await {
        Ok(_) => Ok("过期缓存清理完成".to_string()),
        Err(e) => Err(format!("清理过期缓存失败: {:?}", e)),
    }
}