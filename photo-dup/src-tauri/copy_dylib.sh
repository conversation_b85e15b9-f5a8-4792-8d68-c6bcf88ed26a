#!/bin/bash

# 复制 Swift 动态库到应用包中的脚本

set -e

# 获取参数
APP_BUNDLE_PATH="$1"
DYLIB_SOURCE_PATH="$2"

if [ -z "$APP_BUNDLE_PATH" ] || [ -z "$DYLIB_SOURCE_PATH" ]; then
    echo "Usage: $0 <app_bundle_path> <dylib_source_path>"
    exit 1
fi

# 检查源文件是否存在
if [ ! -f "$DYLIB_SOURCE_PATH" ]; then
    echo "Error: Source dylib not found at $DYLIB_SOURCE_PATH"
    exit 1
fi

# 检查应用包是否存在
if [ ! -d "$APP_BUNDLE_PATH" ]; then
    echo "Error: App bundle not found at $APP_BUNDLE_PATH"
    exit 1
fi

# 复制动态库到应用包的 MacOS 目录
MACOS_DIR="$APP_BUNDLE_PATH/Contents/MacOS"
if [ ! -d "$MACOS_DIR" ]; then
    echo "Error: MacOS directory not found in app bundle"
    exit 1
fi

echo "Copying $DYLIB_SOURCE_PATH to $MACOS_DIR/"
cp "$DYLIB_SOURCE_PATH" "$MACOS_DIR/"

# 验证复制是否成功
DYLIB_NAME=$(basename "$DYLIB_SOURCE_PATH")
if [ -f "$MACOS_DIR/$DYLIB_NAME" ]; then
    echo "Successfully copied $DYLIB_NAME to app bundle"
else
    echo "Error: Failed to copy $DYLIB_NAME to app bundle"
    exit 1
fi

echo "Dynamic library copy completed successfully"
