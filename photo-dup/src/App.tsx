import { useState, useEffect } from 'react';
import { PhotoGalleryContainer } from './components/gallery/PhotoGallery';
import { Button } from './components/ui/button';
import { PhotoInteractionWrapper } from './components/gallery/PhotoInteraction';
import { Loading } from './components/ui/loading';
import { Settings, Image as ImageIcon, Sparkles } from 'lucide-react';
import { photoGalleryService } from './services/photoGallery';
import type { PhotoAsset } from './types';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [photos, setPhotos] = useState<any[]>([]);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 请求权限
      const hasPermission = await photoGalleryService.requestPermission();
      if (!hasPermission) {
        setError('需要访问相册权限，请在系统设置中允许应用访问相册');
        return;
      }

      // 获取相片
      const photoAssets = await photoGalleryService.getPhotoAssets();
      setPhotos(photoAssets);

      // 获取缓存统计
      const stats = await photoGalleryService.getCacheStats();
      setCacheStats(stats);

    } catch (err) {
      console.error('初始化应用失败:', err);
      setError(err instanceof Error ? err.message : '初始化应用失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhotoSelect = (photo: PhotoAsset) => {
    console.log('选择相片:', photo.fileName || photo.id);
  };

  const handlePhotoClick = (photo: PhotoAsset) => {
    console.log('点击相片:', photo.fileName || photo.id);
    // 这里可以实现大图预览功能
  };

  const handleSelectionChange = (selected: PhotoAsset[]) => {
    console.log('选择变化，当前选择:', selected.length, '张相片');
  };

  const handleClearCache = async () => {
    try {
      await photoGalleryService.clearCache();
      const stats = await photoGalleryService.getCacheStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('清除缓存失败:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <ImageIcon className="h-12 w-12 text-primary animate-pulse" />
          </div>
          <Loading text="正在初始化照片库..." size="lg" />
          <p className="text-sm text-muted-foreground">
            正在请求相册访问权限...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center max-w-md space-y-6">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center">
              <svg className="h-8 w-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          
          <div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              无法访问相册
            </h1>
            <p className="text-muted-foreground mb-6">
              {error}
            </p>
          </div>

          <div className="space-y-3">
            <Button onClick={initializeApp} className="w-full">
              重试
            </Button>
            
            <div className="text-xs text-muted-foreground space-y-1">
              <p>如果问题仍然存在，请尝试以下步骤：</p>
              <ol className="list-decimal list-inside space-y-1 text-left">
                <li>打开系统设置</li>
                <li>找到隐私与安全性</li>
                <li>选择照片</li>
                <li>允许此应用访问照片</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 应用头部 */}
      <header className="border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-primary rounded-lg">
                <ImageIcon className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-semibold">照片库管理器</h1>
                <p className="text-sm text-muted-foreground">
                  管理和浏览您的相片收藏
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {cacheStats && (
                <div className="text-xs text-muted-foreground hidden sm:block">
                  缓存: {cacheStats.thumbnailCount} 项, {cacheStats.totalSizeMB.toFixed(1)} MB
                </div>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 设置面板 */}
      {showSettings && (
        <div className="border-b bg-muted/50">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                <span className="font-medium">缓存管理</span>
              </div>
              
              <div className="flex items-center gap-2">
                {cacheStats && (
                  <span className="text-sm text-muted-foreground">
                    {cacheStats.thumbnailCount} 个缩略图 ({cacheStats.totalSizeMB.toFixed(1)} MB)
                  </span>
                )}
                <Button variant="outline" size="sm" onClick={handleClearCache}>
                  清除缓存
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <main className="container mx-auto px-4 py-6">
        <PhotoInteractionWrapper
          photos={photos}
          photosPerRow={6}
          onPhotoSelect={handlePhotoSelect}
          onPhotoClick={handlePhotoClick}
          onSelectionChange={handleSelectionChange}
        >
          <PhotoGalleryContainer />
        </PhotoInteractionWrapper>
      </main>

      {/* 应用底部 */}
      <footer className="border-t bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 mt-8">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-4 w-4" />
              <span>照片库管理器 v1.0</span>
            </div>
            <div className="flex items-center gap-4">
              <span>使用 Rust + Tauri + React 构建</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;