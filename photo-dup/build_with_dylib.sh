#!/bin/bash

# 构建包含动态库的完整应用包

set -e

echo "开始构建 photo-dup 应用..."

# 清理之前的构建
echo "清理之前的构建..."
rm -rf src-tauri/target/release/bundle

# 构建应用
echo "构建 Tauri 应用..."
pnpm tauri build

# 检查构建是否成功
if [ ! -d "src-tauri/target/release/bundle/macos/photo-dup.app" ]; then
    echo "错误: 应用构建失败"
    exit 1
fi

# 复制动态库到应用包
echo "复制 Swift 动态库到应用包..."
./src-tauri/copy_dylib.sh \
    "src-tauri/target/release/bundle/macos/photo-dup.app" \
    "src-tauri/target/release/libPhotoGalleryBridge.dylib"

# 验证动态库是否存在
if [ ! -f "src-tauri/target/release/bundle/macos/photo-dup.app/Contents/MacOS/libPhotoGalleryBridge.dylib" ]; then
    echo "错误: 动态库复制失败"
    exit 1
fi

echo "构建完成！"
echo "应用位置: src-tauri/target/release/bundle/macos/photo-dup.app"
echo "DMG 位置: src-tauri/target/release/bundle/dmg/photo-dup_0.1.0_aarch64.dmg"

# 询问是否启动应用
read -p "是否启动应用进行测试? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "启动应用..."
    open "src-tauri/target/release/bundle/macos/photo-dup.app"
fi

echo "完成！"
